/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Inter', sans-serif;
    line-height: 1.7;
    color: #2d3748;
    overflow-x: hidden;
    background: #fefefe;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    z-index: 1000;
    box-shadow: 0 4px 30px rgba(251, 191, 36, 0.1);
    border-bottom: 1px solid rgba(251, 191, 36, 0.1);
}

.navbar {
    padding: 1.2rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 1.8rem;
    letter-spacing: -0.5px;
}

.nav-logo span {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
    margin-left: 2px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-link {
    text-decoration: none;
    color: #374151;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 8px 0;
}

.nav-link:hover {
    color: #f59e0b;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #334155;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Carousel Section */
.hero-carousel {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
}

.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
}

.carousel-slide.active {
    opacity: 1;
}

.slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.8), rgba(217, 119, 6, 0.6));
}

.slide-content {
    position: relative;
    z-index: 2;
    width: 100%;
    color: white;
    text-align: center;
}

.slide-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.slide-content p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn {
    padding: 14px 28px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: 2px solid transparent;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: white;
    color: #f59e0b;
    border-color: white;
}

/* Carousel Navigation */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 3;
}

.carousel-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: white;
}

/* Carousel Indicators */
.carousel-indicators {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: white;
    transform: scale(1.2);
}

/* Modern Services Section */
.services-modern {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.section-header-modern {
    text-align: center;
    margin-bottom: 5rem;
}

.section-badge {
    display: inline-block;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 10px 24px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.section-header-modern h2 {
    font-size: 3.2rem;
    font-weight: 800;
    background: linear-gradient(135deg, #1f2937, #374151);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    letter-spacing: -1px;
}

.section-header-modern p {
    font-size: 1.3rem;
    color: #6b7280;
    max-width: 650px;
    margin: 0 auto;
    font-weight: 400;
}

.services-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card-modern {
    background: white;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 12px 40px rgba(245, 158, 11, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid rgba(251, 191, 36, 0.1);
}

.service-card-modern:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 60px rgba(245, 158, 11, 0.15);
    border-color: rgba(251, 191, 36, 0.2);
}

.service-image-container {
    position: relative;
    overflow: hidden;
    height: 299px;
}

.service-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.service-card-modern:hover .service-image-container img {
    transform: scale(1.05);
}

.service-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
}

.service-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.service-content-modern {
    padding: 2rem;
}

.service-content-modern h3 {
    font-size: 1.6rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
    letter-spacing: -0.5px;
}

.service-content-modern p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.7;
    font-size: 1rem;
}

.service-features-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.service-features-list span {
    color: #059669;
    font-size: 0.95rem;
    font-weight: 600;
}

.service-btn {
    display: inline-block;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 14px 28px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    font-size: 0.95rem;
}

.service-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    background: linear-gradient(135deg, #d97706, #b45309);
}

/* Modern About Section */
.about-modern {
    padding: 100px 0;
    background: white;
}

.about-content-modern {
    max-width: 1200px;
    margin: 0 auto;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 5rem;
    padding: 3rem;
    background: linear-gradient(135deg, #1e293b, #334155);
    border-radius: 20px;
    color: white;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #60a5fa;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: #e2e8f0;
    font-weight: 500;
}

.about-main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: center;
}

.about-text-modern h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.about-text-modern p {
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 2.5rem;
    line-height: 1.7;
}

.about-highlights {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.highlight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.highlight-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.highlight-content h4 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.highlight-content p {
    color: #64748b;
    font-size: 0.95rem;
    margin: 0;
}

.about-actions {
    display: flex;
    gap: 1rem;
}

.btn-outline {
    background: transparent;
    color: #2563eb;
    border: 2px solid #2563eb;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #2563eb;
    color: white;
    transform: translateY(-2px);
}

.about-image-modern {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.about-image-modern img {
    width: 100%;
    height: auto;
    display: block;
}

.image-badge {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    border-radius: 15px;
    color: #1e293b;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Modern Contact Section */
.contact-modern {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-info-modern h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.contact-info-modern p {
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 3rem;
    line-height: 1.7;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-3px);
}

.method-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.method-content h4 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.method-content p {
    color: #2563eb;
    font-weight: 600;
    margin-bottom: 0.3rem;
    font-size: 1rem;
}

.method-content span {
    color: #64748b;
    font-size: 0.9rem;
}

.contact-cta {
    display: flex;
    gap: 1rem;
}

.quick-contact-form {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.quick-contact-form h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 2rem;
    text-align: center;
}

.contact-form-modern {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row-modern {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.contact-form-modern input,
.contact-form-modern select,
.contact-form-modern textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.contact-form-modern input:focus,
.contact-form-modern select:focus,
.contact-form-modern textarea:focus {
    outline: none;
    border-color: #2563eb;
    background: white;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn-full {
    width: 100%;
    padding: 16px;
    font-size: 1.1rem;
    border-radius: 12px;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #334155;
    color: #94a3b8;
}

/* Service Pages Styles */
.service-hero {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    text-align: center;
}

.service-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.service-hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.service-details {
    padding: 80px 0;
    background: white;
}

.service-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.service-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
}

.service-info p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-item {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #2563eb;
}

.feature-item h3 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.feature-item p {
    color: #64748b;
    font-size: 0.95rem;
}

.specs-list {
    list-style: none;
    padding: 0;
}

.specs-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
    color: #64748b;
}

.specs-list li:before {
    content: "✓";
    color: #2563eb;
    font-weight: bold;
    margin-right: 0.5rem;
}

.service-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.applications {
    padding: 80px 0;
    background: #f8fafc;
}

.applications h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 3rem;
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.application-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
}

.application-item h3 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 1rem;
}

.application-item p {
    color: #64748b;
}

.service-cta {
    padding: 80px 0;
    background: #2563eb;
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.cta-buttons .btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-buttons .btn-secondary:hover {
    background: white;
    color: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .service-hero-content h1 {
        font-size: 2rem;
    }

    .about-content,
    .contact-content,
    .service-content,
    .about-main-content,
    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .services-grid,
    .services-grid-modern,
    .features-grid,
    .applications-grid {
        grid-template-columns: 1fr;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .section-header-modern h2,
    .about-text-modern h2,
    .contact-info-modern h2 {
        font-size: 2rem;
    }

    .form-row-modern {
        grid-template-columns: 1fr;
    }

    .about-actions,
    .contact-cta {
        flex-direction: column;
        align-items: stretch;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .slide-content h1 {
        font-size: 2.5rem;
    }

    .slide-content p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .carousel-nav {
        padding: 0 1rem;
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .quick-contact-form {
        padding: 2rem;
    }

    .services-modern,
    .about-modern,
    .contact-modern {
        padding: 60px 0;
    }
}

/* Page Hero Styles */
.page-hero {
    position: relative;
    height: 60vh;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.page-hero-content {
    position: relative;
    z-index: 2;
}

.page-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.page-hero-content p {
    font-size: 1.2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Company Story Styles */
.company-story {
    padding: 80px 0;
    background: white;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
}

.story-text p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 1.5rem;
}

.achievements {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.achievement {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
}

.achievement h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 0.5rem;
}

.achievement p {
    color: #64748b;
    font-weight: 500;
}

.story-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Values Section */
.our-values {
    padding: 80px 0;
    background: #f8fafc;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    overflow: hidden;
}

.value-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.value-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.value-card p {
    color: #64748b;
}

/* Navigation Active State */
.nav-link.active {
    color: #2563eb;
    font-weight: 600;
}

/* Services Showcase */
.main-services {
    padding: 80px 0;
    background: white;
}

.services-showcase {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.service-showcase-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.service-showcase-item.reverse {
    direction: rtl;
}

.service-showcase-item.reverse > * {
    direction: ltr;
}

.service-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
}

.service-content p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    padding: 0.5rem 0;
    color: #64748b;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li:before {
    content: "✓";
    color: #2563eb;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Contact Page Styles */
.contact-info-section {
    padding: 80px 0;
    background: #f8fafc;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    overflow: hidden;
    background: #eff6ff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-icon img {
    width: 40px;
    height: 40px;
    object-fit: cover;
}

.contact-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.contact-card p {
    color: #64748b;
    margin-bottom: 0.5rem;
}

.contact-link {
    color: #2563eb;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: #1d4ed8;
}

/* Enhanced Contact Form */
.contact-form-section {
    padding: 80px 0;
    background: white;
}

.contact-form-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.form-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
}

.form-info p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 2rem;
}

.form-benefits {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.benefit h4 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.benefit p {
    color: #64748b;
    font-size: 0.95rem;
}

.enhanced-contact-form {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #1e293b;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}

.radio-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
    width: 100%;
}

.form-note {
    font-size: 0.9rem;
    color: #64748b;
    text-align: center;
    margin-top: 1rem;
}

/* Team Section */
.our-team {
    padding: 80px 0;
    background: white;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.team-member {
    text-align: center;
    background: #f8fafc;
    padding: 2rem;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
}

.team-member img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
}

.team-member h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.team-member p {
    color: #2563eb;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.team-member span {
    color: #64748b;
    font-size: 0.9rem;
}
